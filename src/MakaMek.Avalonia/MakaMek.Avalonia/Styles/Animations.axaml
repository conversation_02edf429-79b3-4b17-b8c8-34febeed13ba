<ResourceDictionary xmlns="https://github.com/avaloniaui"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">
    <!-- Animation for damage labels defined as a resource -->
    <Animation Duration="0:0:5" FillMode="Forward" x:Key="DamageLabelAnimation" x:SetterTargetType="TextBlock">
        <!-- Opacity animation from 1.0 to 0.0 -->
        <KeyFrame Cue="0%">
            <Setter Property="Opacity" Value="1.0" />
        </KeyFrame>
        <KeyFrame Cue="100%">
            <Setter Property="Opacity" Value="0.0" />
        </KeyFrame>
    </Animation>
</ResourceDictionary>

