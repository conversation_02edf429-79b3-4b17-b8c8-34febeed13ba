<Styles xmlns="https://github.com/avaloniaui"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">

    <!-- Progress Bar Styles -->
    <Style Selector="ProgressBar.unitPartHealthBar">
        <Setter Property="Orientation" Value="Vertical"/>
        <Setter Property="Width" Value="25"/>
        <Setter Property="Margin" Value="2,1"/>
        <Setter Property="MinHeight" Value="0"/>
        <Setter Property="ShowProgressText" Value="True"/>
        <Setter Property="ProgressTextFormat" Value="{}{0}/{3}"/>
        <Setter Property="VerticalAlignment" Value="Stretch"/>
    </Style>

    <Style Selector="ProgressBar.mechArmor">
        <Setter Property="Foreground" Value="{DynamicResource MechArmorBrush}"/>
    </Style>

    <Style Selector="ProgressBar.mechStructure">
        <Setter Property="Foreground" Value="{DynamicResource MechStructureBrush}"/>
    </Style>
    
    <!-- Progressbar style for info panels -->
    <Style Selector="ProgressBar.infoPanel">
        <Setter Property="Foreground" Value="{DynamicResource ErrorBrush}"/>
        <Setter Property="Background" Value="{DynamicResource BorderBrush}"/>
        <Setter Property="Height" Value="20"/>
        <Setter Property="MinHeight" Value="0"/>
    </Style>
    
    <!-- Destroyed part styles -->
    <Style Selector="ProgressBar.destroyed">
        <Setter Property="Background" Value="{DynamicResource DestroyedBrush}"/>
    </Style>
    
    <!-- ListBox Styles -->
    <Style Selector="ListBox.gameList">
        <Setter Property="Background" Value="{DynamicResource SurfaceBrush}"/>
        <Setter Property="BorderBrush" Value="{DynamicResource BorderBrush}"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="CornerRadius" Value="4"/>
    </Style>
    
    <Style Selector="ListBoxItem.gameListItem">
        <Setter Property="Padding" Value="10,8"/>
        <Setter Property="CornerRadius" Value="4"/>
        <Setter Property="Margin" Value="2"/>
    </Style>
    
    <Style Selector="ListBoxItem.gameListItem:selected">
        <Setter Property="Background" Value="{DynamicResource PrimaryLightBrush}"/>
    </Style>
    
    <Style Selector="ListBoxItem.gameListItem:pointerover">
        <Setter Property="Background" Value="{DynamicResource SurfaceBrush}"/>
    </Style>
    
    <!-- Slider Styles -->
    <Style Selector="Slider.gameSlider">
        <Setter Property="Minimum" Value="0"/>
        <Setter Property="Maximum" Value="100"/>
        <Setter Property="TickFrequency" Value="5"/>
        <Setter Property="IsSnapToTickEnabled" Value="True"/>
    </Style>
    
    <Style Selector="Slider.gameSlider /template/ Thumb">
        <Setter Property="Background" Value="{DynamicResource PrimaryBrush}"/>
    </Style>
    
    <Style Selector="Slider.gameSlider /template/ RepeatButton#PART_IncreaseButton">
        <Setter Property="Background" Value="{DynamicResource PrimaryLightBrush}"/>
    </Style>
    
    <!-- CheckBox Styles -->
    <Style Selector="CheckBox.gameCheckBox">
        <Setter Property="Margin" Value="0,0,10,0"/>
    </Style>
    
    <Style Selector="CheckBox.gameCheckBox /template/ Border#NormalRectangle">
        <Setter Property="BorderBrush" Value="{DynamicResource PrimaryBrush}"/>
    </Style>
    
    <Style Selector="CheckBox.gameCheckBox:checked /template/ Border#NormalRectangle">
        <Setter Property="Background" Value="{DynamicResource PrimaryBrush}"/>
    </Style>
    
    <!-- TabControl Styles -->
    <Style Selector="TabControl.gameTabControl">
        <Setter Property="Background" Value="{DynamicResource BackgroundBrush}"/>
        <Setter Property="BorderBrush" Value="{DynamicResource BorderBrush}"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="Padding" Value="10"/>
    </Style>
    
    <Style Selector="TabItem.gameTabItem">
        <Setter Property="FontWeight" Value="SemiBold"/>
        <Setter Property="Padding" Value="15,8"/>
        <Setter Property="Margin" Value="0,0,4,0"/>
        <Setter Property="Foreground" Value="{DynamicResource TextLightBrush}"/>
    </Style>
    
    <Style Selector="TabItem.gameTabItem:selected">
        <Setter Property="Background" Value="{DynamicResource PrimaryLightBrush}"/>
        <Setter Property="Foreground" Value="{DynamicResource TextBrush}"/>
    </Style>
    
    <!-- ScrollViewer Styles -->
    <Style Selector="ScrollViewer.gameScrollViewer">
        <Setter Property="HorizontalScrollBarVisibility" Value="Auto"/>
        <Setter Property="VerticalScrollBarVisibility" Value="Auto"/>
    </Style>
</Styles>

