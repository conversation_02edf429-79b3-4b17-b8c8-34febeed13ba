<UserControl xmlns="https://github.com/avaloniaui"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:viewModels1="clr-namespace:Sanet.MakaMek.Presentation.ViewModels;assembly=Sanet.MakaMek.Presentation"
             mc:Ignorable="d" d:DesignWidth="800" d:DesignHeight="450"
             x:Class="Sanet.MakaMek.Avalonia.Views.NewGame.Fragments.PlayersFragment"
             x:DataType="viewModels1:NewGameViewModel">
    <StackPanel Classes="verticalSpaced">
        <Button Content="Add Player" Command="{Binding AddPlayerCommand}"
                Classes="secondary"
                HorizontalAlignment="Center"
                IsEnabled="{Binding CanAddPlayer}"/>
        <ItemsControl ItemsSource="{Binding Players}">
            <ItemsControl.ItemTemplate>
                <DataTemplate>
                    <Border Classes="card" Margin="0,5">
                        <Grid ColumnDefinitions="*,*" 
                              RowDefinitions="Auto,*">
                            <StackPanel Grid.Row="0" Grid.Column="0" Classes="verticalSpaced" Margin="10,5">
                                <TextBlock Text="{Binding Name}" Classes="h3" />
                                <TextBlock Text="Available Units:" Classes="label" Margin="5"/>
                                <ComboBox 
                                    IsVisible="{Binding IsLocalPlayer}"
                                    IsEnabled="{Binding CanSelectUnit}"
                                    ItemsSource="{Binding AvailableUnits}"
                                          SelectedItem="{Binding SelectedUnit, Mode=TwoWay}">
                                    <ComboBox.ItemTemplate>
                                        <DataTemplate>
                                            <TextBlock Text="{Binding Chassis}" Classes="body"/>
                                        </DataTemplate>
                                    </ComboBox.ItemTemplate>
                                </ComboBox>
                                <Button Content="Add Unit" 
                                        IsVisible="{Binding IsLocalPlayer}"
                                        Command="{Binding AddUnitCommand}" 
                                        Classes="primary"
                                        IsEnabled="{Binding CanAddUnit}"/>
                            </StackPanel>
                            <StackPanel Grid.Row="1" Grid.Column="0" Orientation="Horizontal">
                                <Button Content="Join Game" Command="{Binding JoinGameCommand}" 
                                         Classes="primary"
                                         IsVisible="{Binding CanJoin}"/>
                                <Button Content="Set Ready" Command="{Binding SetReadyCommand}" 
                                         Classes="primary"
                                         IsVisible="{Binding CanSetReady}"/>
                            </StackPanel>
                            <Border Grid.Row="0" Grid.Column="1" Classes="card" Background="{DynamicResource SurfaceBrush}">
                                <ItemsControl ItemsSource="{Binding Units}">
                                    <ItemsControl.ItemTemplate>
                                        <DataTemplate>
                                            <TextBlock Text="{Binding Chassis}" Classes="body" Margin="2"/>
                                        </DataTemplate>
                                    </ItemsControl.ItemTemplate>
                                </ItemsControl>
                            </Border>
                        </Grid>
                    </Border>
                </DataTemplate>
            </ItemsControl.ItemTemplate>
        </ItemsControl>
    </StackPanel>
</UserControl>

