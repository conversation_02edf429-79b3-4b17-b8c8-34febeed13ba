<UserControl xmlns="https://github.com/avaloniaui"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:fragments1="clr-namespace:Sanet.MakaMek.Avalonia.Views.NewGame.Fragments"
             xmlns:viewModels1="clr-namespace:Sanet.MakaMek.Presentation.ViewModels;assembly=Sanet.MakaMek.Presentation"
             mc:Ignorable="d" d:DesignWidth="800" d:DesignHeight="450"
             x:Class="Sanet.MakaMek.Avalonia.Views.StartNewGame.StartNewGameViewWide"
             x:DataType="viewModels1:StartNewGameViewModel">
    <DockPanel Classes="pageContainer">
        <Button DockPanel.Dock="Bottom"
                Command="{Binding StartGameCommand}"
                Content="Start Game"
                Classes="primary"
                IsEnabled="{Binding CanStartGame}"
                HorizontalAlignment="Center"
                Margin="0,10,0,0"/>
        
        <ScrollViewer Classes="gameScrollViewer">
            <Grid 
                ColumnDefinitions="*,*"
                RowDefinitions="Auto,*,Auto">
                    <TextBlock Grid.Row="0" Grid.Column="0" Text="Map" Classes="h2" HorizontalAlignment="Center"/>
                    <TextBlock Grid.Row="0" Grid.Column="1" Text="Players" Classes="h2" HorizontalAlignment="Center"/>
                    <fragments1:MapConfigFragment Grid.Column="0" Grid.Row="1" Margin="10"/>
                    <fragments1:NetworkFragment Grid.Column="0" Grid.Row="2" IsVisible="{Binding CanStartLanServer}" Margin="10"/>
                    <fragments1:PlayersFragment Grid.Column="1" Grid.Row="1" Grid.RowSpan="2" Margin="10"/>
            </Grid>
        </ScrollViewer>
    </DockPanel>
</UserControl>

