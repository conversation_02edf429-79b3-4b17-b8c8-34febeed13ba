<UserControl xmlns="https://github.com/avaloniaui"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:viewModels="clr-namespace:Sanet.MakaMek.Presentation.ViewModels;assembly=Sanet.MakaMek.Presentation"
             mc:Ignorable="d" d:DesignWidth="400" d:DesignHeight="100"
             x:Class="Sanet.MakaMek.Avalonia.Views.JoinGame.Fragments.ConnectFragment"
             x:DataType="viewModels:JoinGameViewModel">

  <StackPanel Spacing="10">
    <TextBlock Text="Server Address:" FontWeight="Bold"/>
    <TextBox Text="{Binding ServerIp, Mode=TwoWay}"
             Watermark="Enter Server IP"/>
    <Button Content="Connect"
            Command="{Binding ConnectCommand}"
            IsEnabled="{Binding CanConnect}" 
            HorizontalAlignment="Stretch"/>
     <!-- Optional: Add a status indicator -->
     <TextBlock Text="Connected" 
                IsVisible="{Binding IsConnected}" 
                Foreground="Green"/> 
  </StackPanel>

</UserControl>

