<Styles xmlns="https://github.com/avaloniaui"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:controls="using:Sanet.MakaMek.Avalonia.Views.TemplatedControls">
    <Design.PreviewWith>
        <controls:GamePanel />
    </Design.PreviewWith>

    <Style Selector="controls|GamePanel">
        <Setter Property="Template">
            <ControlTemplate>
                <Border Background="{DynamicResource BackgroundBrush}"
                        BorderBrush="{DynamicResource PrimaryBrush}"
                        BorderThickness="1"
                        CornerRadius="8"
                        Width="300"
                        Height="400"
                        HorizontalAlignment="Right"
                        VerticalAlignment="Bottom"
                        Margin="10">
                    <Grid RowDefinitions="Auto,*">
                        <!-- Header -->
                        <Grid Grid.Row="0" 
                              Background="{DynamicResource PrimaryBrush}" 
                              Height="40">
                            <TextBlock Text="{TemplateBinding Title}"
                                     VerticalAlignment="Center"
                                     HorizontalAlignment="Center"
                                     FontWeight="Bold"
                                     Foreground="White"/>
                            <Button Command="{TemplateBinding CloseCommand}"
                                    HorizontalAlignment="Right"
                                    Width="40"
                                    Height="40"
                                    Classes="icon"
                                    Background="Transparent">
                                <PathIcon Data="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z" 
                                          Foreground="White"/>
                            </Button>
                        </Grid>
                        <!-- Content -->
                        <ContentPresenter Grid.Row="1" 
                                        Content="{TemplateBinding Content}"/>
                    </Grid>
                </Border>
            </ControlTemplate>
        </Setter>
    </Style>
</Styles>

