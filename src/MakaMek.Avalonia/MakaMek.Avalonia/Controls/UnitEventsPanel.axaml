<UserControl xmlns="https://github.com/avaloniaui"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:converters="clr-namespace:Sanet.MakaMek.Avalonia.Converters"
             xmlns:viewModels1="clr-namespace:Sanet.MakaMek.Presentation.ViewModels;assembly=Sanet.MakaMek.Presentation"
             mc:Ignorable="d" d:DesignWidth="400" d:DesignHeight="450"
             x:DataType="viewModels1:BattleMapViewModel"
             x:Class="Sanet.MakaMek.Avalonia.Controls.UnitEventsPanel">
    <UserControl.Resources>
        <converters:EventTypeToBackgroundConverter x:Key="EventTypeToBackgroundConverter"/>
    </UserControl.Resources>
    
    <ScrollViewer>
        <ItemsControl ItemsSource="{Binding SelectedUnitEvents}" Margin="10">
            <ItemsControl.ItemTemplate>
                <DataTemplate>
                    <TextBlock Margin="5" Padding="5" Text="{Binding FormattedText}" 
                               Classes="bodyLarge"
                               Background="{Binding Type, Converter={StaticResource EventTypeToBackgroundConverter}}"/>
                </DataTemplate>
            </ItemsControl.ItemTemplate>
        </ItemsControl>
    </ScrollViewer>
</UserControl>

