<UserControl xmlns="https://github.com/avaloniaui"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:units="clr-namespace:Sanet.MakaMek.Core.Models.Units;assembly=Sanet.MakaMek.Core"
             xmlns:converters="clr-namespace:Sanet.MakaMek.Avalonia.Converters;assembly=Sanet.MakaMek.Avalonia"
             x:DataType="units:Unit"
             x:Class="Sanet.MakaMek.Avalonia.Controls.UnitMovementInfoPanel"
             mc:Ignorable="d">
    <UserControl.Resources>
        <converters:CollectionToVisibilityConverter x:Key="CollectionToVisibilityConverter"/>
        <converters:ModifierToTextConverter x:Key="ModifierToTextConverter"/>
    </UserControl.Resources>
    <StackPanel Spacing="2" Orientation="Vertical">
        <Border Background="#20000000"
            CornerRadius="4"
            Padding="10"
            IsVisible="{Binding HasMoved}">
       
            <Grid ColumnDefinitions="*,*,*" RowDefinitions="Auto,Auto">
                <TextBlock Grid.Row="0" Grid.Column="0"
                         Text="Type"
                         HorizontalAlignment="Center"/>
                <TextBlock Grid.Row="0" Grid.Column="1"
                         Text="Points"
                         HorizontalAlignment="Center"/>
                <TextBlock Grid.Row="0" Grid.Column="2"
                         Text="Distance"
                         HorizontalAlignment="Center"/>

                <TextBlock Grid.Row="1" Grid.Column="0"
                         Text="{Binding MovementTypeUsed}"
                         HorizontalAlignment="Center"/>
                <TextBlock Grid.Row="1" Grid.Column="1"
                         Text="{Binding MovementPointsSpent}"
                         HorizontalAlignment="Center"/>
                <TextBlock Grid.Row="1" Grid.Column="2"
                         Text="{Binding DistanceCovered}"
                         HorizontalAlignment="Center"/>
            </Grid>
        </Border>
         <!-- Movement Penalties Section -->
        <ItemsControl ItemsSource="{Binding MovementModifiers}"
                       IsVisible="{Binding MovementModifiers, Converter={StaticResource CollectionToVisibilityConverter}}"
                       Margin="0,10,0,0">
          <ItemsControl.ItemsPanel>
             <ItemsPanelTemplate>
                 <WrapPanel ItemSpacing="2" LineSpacing="2" Orientation="Horizontal"/>
             </ItemsPanelTemplate>
          </ItemsControl.ItemsPanel>
          <ItemsControl.ItemTemplate>
             <DataTemplate>
                 <Border Classes="statusTag">
                     <TextBlock Text="{Binding ., Converter={StaticResource ModifierToTextConverter}}"/>
                 </Border>
             </DataTemplate>
          </ItemsControl.ItemTemplate>
        </ItemsControl>
    </StackPanel>
</UserControl>

