<UserControl xmlns="https://github.com/avaloniaui"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:weapons="clr-namespace:Sanet.MakaMek.Core.Models.Units.Components.Weapons;assembly=Sanet.MakaMek.Core"
             xmlns:converters="clr-namespace:Sanet.MakaMek.Avalonia.Converters"
             xmlns:controls="clr-namespace:Sanet.MakaMek.Avalonia.Controls"
             mc:Ignorable="d" d:DesignWidth="300" d:DesignHeight="300"
             x:Class="Sanet.MakaMek.Avalonia.Controls.UnitWeaponsPanel">
    <UserControl.Resources>
        <converters:WeaponRangeConverter x:Key="WeaponRangeConverter"/>
    </UserControl.Resources>
    <!-- Headers -->
    <Grid ColumnDefinitions="4*,2*,*,3*"
          RowDefinitions="Auto,*"
          Margin="5">
        <TextBlock Grid.Column="0" Text="Weapon" Classes="h4"/>
        <TextBlock Grid.Column="1" Text="DMG" Classes="h4" Margin="5,0"/>
        <TextBlock Grid.Column="2" Text="HT" Classes="h4" Margin="5,0"/>
        <TextBlock Grid.Column="3" Text="Range" Classes="h4" Margin="5,0"/>
        <ScrollViewer
            Grid.ColumnSpan="4"
            Grid.Row="1"
            Classes="gameScrollViewer">
                    <ItemsControl Name="WeaponsGroup">
                            <ItemsControl.ItemTemplate>
                                <DataTemplate DataType="controls:ComponentGroup">
                                    <StackPanel>
                                        <TextBlock Text="{Binding MountedOn}" Classes="h4"/>
                                        <ItemsControl Name="WeaponsList" ItemsSource="{Binding Components}">
                                            <ItemsControl.ItemTemplate>
                                                <DataTemplate DataType="weapons:Weapon">
                                                    <Border Margin="2">
                                                        <Grid ColumnDefinitions="4*,2*,*,3*" Margin="5,2" 
                                                              Background="{Binding ., Converter={StaticResource ComponentStatusBackgroundConverter}}">
                                                            <TextBlock Grid.Column="0" Text="{Binding Name}" Classes="body" VerticalAlignment="Center"/>
                                                            <TextBlock Grid.Column="1" Text="{Binding Damage}" Classes="body" Margin="5,0" VerticalAlignment="Center"/>
                                                            <TextBlock Grid.Column="2" Text="{Binding Heat}" Classes="body" Margin="5,0" VerticalAlignment="Center"/>
                                                            <TextBlock Grid.Column="3" 
                                                                    Text="{Binding ., Converter={StaticResource WeaponRangeConverter}}" 
                                                                    Classes="body"
                                                                    Margin="5,0" 
                                                                    VerticalAlignment="Center"/>
                                                        </Grid>
                                                    </Border>
                                                </DataTemplate>
                                                </ItemsControl.ItemTemplate>
                                            </ItemsControl>
                                    </StackPanel>
                                </DataTemplate>
                            </ItemsControl.ItemTemplate>
                        </ItemsControl>
                </ScrollViewer>
    </Grid>
</UserControl>

