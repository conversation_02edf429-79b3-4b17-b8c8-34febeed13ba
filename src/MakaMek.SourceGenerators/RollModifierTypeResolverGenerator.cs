using Microsoft.CodeAnalysis;
using Microsoft.CodeAnalysis.Text;
using System.Text;

namespace Sanet.MakaMek.SourceGenerators;

[Generator]
public class RollModifierTypeResolverGenerator : ISourceGenerator
{
    public void Initialize(GeneratorInitializationContext context)
    {
    }

    public void Execute(GeneratorExecutionContext context)
    {
        // Get the compilation
        var compilation = context.Compilation;

        // Find the RollModifier type
        var rollModifierSymbol =
            compilation.GetTypeByMetadataName("Sanet.MakaMek.Core.Models.Game.Mechanics.Modifiers.RollModifier");
        if (rollModifierSymbol == null)
        {
            throw new Exception("RollModifier type not found");
        }

        // Now that we have the RollModifier type, find all derived classes
        var derivedClasses = new List<INamedTypeSymbol>();

        // Get all types from the compilation more efficiently
        var allTypes = compilation.GlobalNamespace.GetNamespaceMembers()
            .SelectMany(GetAllTypesRecursively)
            .Concat(compilation.GlobalNamespace.GetTypeMembers());

        foreach (var typeSymbol in allTypes)
        {
            // Skip abstract types and check inheritance
            if (typeSymbol.IsAbstract || typeSymbol.TypeKind != TypeKind.Class)
                continue;
            if (InheritsFrom(typeSymbol, rollModifierSymbol))
            {
                derivedClasses.Add(typeSymbol);
            }
        }
        
        // Add diagnostic to help debugging
        if (derivedClasses.Count == 0)
        {
            throw new Exception("No derived classes found");
        }

        // Generate the source code
        var source =
            GenerateTypeResolverExtension(rollModifierSymbol.ContainingNamespace.ToDisplayString(), derivedClasses);

        // Add the source code to the compilation
        context.AddSource("RollModifierTypeResolverExtension.g.cs", SourceText.From(source, Encoding.UTF8));
    }

    private bool InheritsFrom(INamedTypeSymbol classSymbol, INamedTypeSymbol baseTypeSymbol)
    {
        // Check if the class directly inherits from the base type
        if (classSymbol.BaseType != null &&
            SymbolEqualityComparer.Default.Equals(classSymbol.BaseType, baseTypeSymbol))
        {
            return true;
        }

        // Check inheritance chain
        var currentSymbol = classSymbol.BaseType;
        while (currentSymbol != null)
        {
            if (SymbolEqualityComparer.Default.Equals(currentSymbol, baseTypeSymbol))
                return true;

            // Check if the base type is a constructed generic type
            if (currentSymbol is { IsGenericType: true })
            {
                if (SymbolEqualityComparer.Default.Equals(currentSymbol.ConstructedFrom, baseTypeSymbol))
                    return true;
            }

            currentSymbol = currentSymbol.BaseType;
        }

        // Check if the class implements the base type as an interface
        foreach (var interfaceSymbol in classSymbol.AllInterfaces)
        {
            if (SymbolEqualityComparer.Default.Equals(interfaceSymbol, baseTypeSymbol))
                return true;
        }

        return false;
    }

    private string GenerateTypeResolverExtension(string rollModifierNamespace, List<INamedTypeSymbol> derivedClasses)
    {
        var sb = new StringBuilder();

        sb.AppendLine("// <auto-generated/>");
        sb.AppendLine("using System;");
        sb.AppendLine("using System.Text.Json;");
        sb.AppendLine("using System.Text.Json.Serialization;");
        sb.AppendLine("using System.Text.Json.Serialization.Metadata;");
        sb.AppendLine($"using {rollModifierNamespace};");

        // Add imports for all namespaces containing derived classes
        var namespaces = derivedClasses
            .Select(c => c.ContainingNamespace.ToDisplayString())
            .Distinct()
            .Where(ns => ns != rollModifierNamespace)
            .OrderBy(ns => ns);

        foreach (var ns in namespaces)
        {
            sb.AppendLine($"using {ns};");
        }

        sb.AppendLine();
        sb.AppendLine("namespace Sanet.MakaMek.Core.Services.Transport");
        sb.AppendLine("{");
        sb.AppendLine("    /// <summary>");
        sb.AppendLine("    /// Extension for RollModifierTypeResolver with auto-generated derived types");
        sb.AppendLine("    /// Generated automatically by RollModifierTypeResolverGenerator");
        sb.AppendLine("    /// </summary>");
        sb.AppendLine("    public partial class RollModifierTypeResolver");
        sb.AppendLine("    {");
        sb.AppendLine("        /// <summary>");
        sb.AppendLine("        /// Registers all known RollModifier derived types");
        sb.AppendLine("        /// Generated automatically by RollModifierTypeResolverGenerator");
        sb.AppendLine("        /// </summary>");
        sb.AppendLine("        static partial void RegisterGeneratedTypes(JsonTypeInfo jsonTypeInfo)");
        sb.AppendLine("        {");
        sb.AppendLine("            // Add all known derived types generated by the source generator");

        // Add a line for each derived class
        foreach (var derivedClass in derivedClasses.OrderBy(c => c.Name))
        {
            string fullClassName = derivedClass.ToDisplayString();
            string className = derivedClass.Name;
            sb.AppendLine(
                $"            jsonTypeInfo.PolymorphismOptions.DerivedTypes.Add(new(typeof({fullClassName}), \"{className}\"));");
        }

        sb.AppendLine("        }");
        sb.AppendLine("    }");
        sb.AppendLine("}");

        return sb.ToString();
    }

    private static IEnumerable<INamedTypeSymbol> GetAllTypesRecursively(INamespaceSymbol namespaceSymbol)
    {
        foreach (var type in namespaceSymbol.GetTypeMembers())
            yield return type;

        foreach (var nestedNamespace in namespaceSymbol.GetNamespaceMembers())
        {
            foreach (var type in GetAllTypesRecursively(nestedNamespace))
                yield return type;
        }
    }
}