using Sanet.MakaMek.Core.Data.Game.Mechanics;
using Sanet.MakaMek.Core.Models.Units;

namespace Sanet.MakaMek.Core.Models.Game.Mechanics.Mechs.Falling;

/// <summary>
/// Interface for calculating piloting skill roll target numbers
/// </summary>
public interface IPilotingSkillCalculator
{
    /// <summary>
    /// Gets a detailed breakdown of all modifiers affecting the piloting skill roll.
    /// </summary>
    /// <param name="unit">The unit making the piloting skill roll.</param>
    /// <param name="rollType">The specific Piloting Skill Roll type to consider.</param>
    /// <param name="game">The game instance, used for accessing the map and other game state.</param>
    /// <returns>A breakdown of the piloting skill roll calculation</returns>
    PsrBreakdown GetPsrBreakdown(Unit unit, PilotingSkillRollType rollType, IGame? game = null);

    /// <summary>
    /// Evaluates a piloting skill roll and returns complete roll data.
    /// </summary>
    /// <param name="psrBreakdown">The PSR breakdown containing target number and modifiers.</param>
    /// <param name="unit">The unit making the piloting skill roll.</param>
    /// <param name="rollType">The type of piloting skill roll.</param>
    /// <returns>Complete piloting skill roll data including dice results.</returns>
    PilotingSkillRollData EvaluateRoll(PsrBreakdown psrBreakdown, Unit unit, PilotingSkillRollType rollType);
}
