<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <IsPackable>false</IsPackable>
    <AssemblyName>Sanet.MakaMek.Presentation.Tests</AssemblyName>
    <RootNamespace>Sanet.MakaMek.Presentation.Tests</RootNamespace>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="coverlet.collector" Version="6.0.4">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="coverlet.msbuild" Version="6.0.4">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="JetBrains.Annotations" Version="2025.2.0" />
    <PackageReference Include="Microsoft.NET.Test.Sdk" Version="17.14.1" />
    <PackageReference Include="NSubstitute" Version="5.3.0" />
    <PackageReference Include="Shouldly" Version="4.3.0" />
    <PackageReference Include="xunit" Version="2.9.3" />
    <PackageReference Include="xunit.runner.visualstudio" Version="3.1.3">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
  </ItemGroup>

  <ItemGroup>
    <Using Include="Xunit" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\src\MakaMek.Core\MakaMek.Core.csproj" />
    <ProjectReference Include="..\..\src\MakaMek.Presentation\MakaMek.Presentation.csproj" />
    <ProjectReference Include="..\MakaMek.Core.Tests\MakaMek.Core.Tests.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Content Include="..\..\src\MakaMek.Avalonia\MakaMek.Avalonia\Resources\Units\Mechs\LCT-1V.mtf">
      <Link>Resources\Mechs\LCT-1V.mtf</Link>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="..\..\src\MakaMek.Avalonia\MakaMek.Avalonia\Resources\Units\Mechs\SHD-2D.mtf">
      <Link>Resources\Mechs\SHD-2D.mtf</Link>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
  </ItemGroup>

</Project>
